<template>
  <div class="job-offers">
    <!-- Header -->
    <div class="job-offers-header">
      <h3 class="title">
        Job Offers & Messages
        <span v-if="loading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i>
        </span>
      </h3>

      <div class="controls">
        <button
          @click="refreshData"
          :disabled="loading"
          class="refresh-btn"
          title="Refresh job offers">
          <i class="fas fa-refresh" :class="{ 'fa-spin': loading }"></i>
        </button>

        <button
          @click="togglePolling"
          :class="['polling-btn', { active: isPolling }]"
          :title="isPolling ? 'Stop auto-refresh' : 'Start auto-refresh'">
          <i :class="isPolling ? 'fas fa-pause' : 'fas fa-play'"></i>
        </button>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error }}
      <button @click="clearError" class="close-error">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Content -->
    <div class="job-offers-content">
      <!-- No Data State -->
      <div v-if="!hasData && !loading" class="no-data">
        <i class="fas fa-inbox"></i>
        <p>No job offers or messages available</p>
        <button @click="refreshData" class="retry-btn">
          <i class="fas fa-refresh"></i>
          Refresh
        </button>
      </div>

      <!-- Job Offers Section -->
      <div v-if="jobs.length > 0" class="jobs-section">
        <h4 class="section-title">
          Job Offers ({{ jobs.length }})
          <span v-if="highPriorityJobs.length > 0" class="priority-badge">
            {{ highPriorityJobs.length }} High Priority
          </span>
        </h4>

        <div class="jobs-list">
          <!-- <JobOfferItem
            v-for="job in jobs"
            :key="job.Key"
            :job="job"
            @job-selected="onJobSelected"
            @job-action="onJobAction"
          /> -->
        </div>
      </div>

      <!-- Messages Section -->
      <div v-if="messages.length > 0" class="messages-section">
        <h4 class="section-title">
          Messages ({{ messages.length }})
          <span v-if="urgentMessages.length > 0" class="urgent-badge">
            {{ urgentMessages.length }} Urgent
          </span>
        </h4>

        <div class="messages-list">
          <!-- <JobMessageItem
            v-for="message in messages"
            :key="message.Key"
            :message="message"
            @message-acknowledged="onMessageAcknowledged"
          /> -->
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading && !hasData" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading job offers...</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from '@vue/composition-api';
import { useJobOffersStore } from './jobOffers.store';
import { storeToRefs } from 'pinia';
import JobOfferItem from './JobOfferItem.vue';
import JobMessageItem from './JobMessageItem.vue';

export default {
  name: 'JobOffers',
  components: {
    JobOfferItem,
    JobMessageItem
  },
  setup() {
    const pollingTimer = ref(null);
    const pollingInterval = 30000;
    const isPollingActive = ref(false);

    const store = useJobOffersStore();
    const {
      jobs,
      messages,
      loading,
      error
    } = storeToRefs(store);

    const hasData = computed(() =>
      (Array.isArray(jobs.value) && jobs.value.length > 0) ||
      (Array.isArray(messages.value) && messages.value.length > 0)
    );

    const urgentMessages = computed(() =>
      Array.isArray(messages.value) ? messages.value.filter(msg => msg.urgent) : []
    );

    const highPriorityJobs = computed(() =>
      Array.isArray(jobs.value) ? jobs.value.filter(job => job.priority >= 5) : []
    );

    const isPolling = computed(() => isPollingActive.value);

    async function loadJobs() {
      try {
        await store.fetchNewOffers();
      } catch (err) {
        console.error('Failed to load job offers:', err);
      }
    }

    async function refreshData() {
      await loadJobs();
    }

    function startPolling() {
      if (isPollingActive.value) return;
      isPollingActive.value = true;
      pollingTimer.value = setInterval(async () => {
        if (!loading.value) {
          await loadJobs();
        }
      }, pollingInterval);
    }

    function stopPolling() {
      if (pollingTimer.value) {
        clearInterval(pollingTimer.value);
        pollingTimer.value = null;
      }
      isPollingActive.value = false;
    }

    function togglePolling() {
      isPollingActive.value ? stopPolling() : startPolling();
    }

    function clearError() {
      store.clearError();
    }

    function onJobSelected(job) {
      console.log('Job selected:', job);
    }

    function onJobAction(actionData) {
      const { job, action } = actionData;
      console.log('Job action triggered:', job, action);
    }

    function onMessageAcknowledged(message) {
      console.log('Message acknowledged:', message);
    }

    onMounted(async () => {
      await loadJobs();
      startPolling();
    });

    onBeforeUnmount(() => {
      stopPolling();
    });

    return {
      jobs,
      messages,
      loading,
      error,
      hasData,
      urgentMessages,
      highPriorityJobs,
      isPolling,
      refreshData,
      togglePolling,
      clearError,
      onJobSelected,
      onJobAction,
      onMessageAcknowledged
    };
  }
};
</script>

<style scoped>
.job-offers {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.job-offers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-indicator {
  color: #6b7280;
}

.controls {
  display: flex;
  gap: 8px;
}

.refresh-btn,
.polling-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: #fff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover,
.polling-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.polling-btn.active {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #fef2f2;
  border-bottom: 1px solid #fecaca;
  color: #dc2626;
}

.close-error {
  margin-left: auto;
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 4px;
}

.job-offers-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 12px;
}

.priority-badge,
.urgent-badge {
  background: #dc2626;
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.urgent-badge {
  background: #f59e0b;
}

.jobs-section,
.messages-section {
  margin-bottom: 32px;
}

.jobs-list,
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.loading-spinner {
  text-align: center;
  color: #6b7280;
}

.loading-spinner i {
  font-size: 32px;
  margin-bottom: 12px;
}
</style>
