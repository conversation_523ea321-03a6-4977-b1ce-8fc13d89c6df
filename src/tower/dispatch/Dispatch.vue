<template>
  <div class="dispatch">
    <div class="grid-container">
      <transition name="fade" mode="out-in">
        <grid
          title="Unassigned Calls"
          key="grid"
          class="grid unassigned"
          :grid="unassignedGridSettings"
          :data="unassignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :quickAssign="true"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          :gridType="'unassigned'"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleAllAppointments()"><i :class="appointmentsToggleClasses"></i>&nbsp;&nbsp;All Appointments</app-button>
          </template>
        </grid>
      </transition>

      <div class="resizer"></div>

      <transition name="fade" mode="out-in">
        <grid
          title="Assigned Calls"
          key="grid"
          class="grid assigned"
          :grid="assignedGridSettings"
          :data="assignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          :gridType="'assigned'"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleDispatchUnits">Dispatch Units</app-button> &nbsp;
            <app-button type="white" @click="notify">Notify</app-button>
          </template>

          <template slot="floating-tools">
            <app-button type="success" size="normal" @click="addCall" v-if="canAddCall">
              <i class="far fa-plus"></i>&nbsp;Add
            </app-button>
          </template>
        </grid>
      </transition>

      <job-offers class="job-offers" />
      <!-- <div class="job-offers"></div> -->
    </div> <!-- /grid-container -->

    <actions
      :show="actionsVisible"
      :record="actionableRecord"
      :call-key="actionableRecord.lCallKey"
      :subterminal-key="actionableRecord.lSubterminalKey"
      :dispatch-key="actionableRecord.lDispatchKey"
      @close="toggleActions"
      @notify="notify">
    </actions>

    <app-modal title="Payments" @close="$_PaymentMixin_togglePayments" :show="paymentsVisible">
      <payment-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="paymentsVisible"
        @close="$_PaymentMixin_togglePayments(false)">
      </payment-section>
    </app-modal>

    <app-modal title="Holds" @close="$_HoldsMixin_toggleHolds" :show="holdsVisible">
      <holds-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="holdsVisible"
        @close="$_HoldsMixin_toggleHolds(false)">
      </holds-section>
    </app-modal>

    <app-modal title="Dispatch Units" @close="toggleDispatchUnits" :show="dispatchUnitsVisible" :pad="false">
      <DispatchUnitsController />
    </app-modal>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div> <!-- /dispatch -->
</template>

<script>
import is from 'is_js';
import Access from '@/utils/access.js';
import { mapGetters } from 'vuex';
import keyCodes from '@/utils/keycodes.js';
import Actions from '@/tower/actions/Actions.vue';
import Grid from '@/components/features/DispatchGrid.vue';
import HoldsSection from '@/components/call/HoldsSection.vue';
import HoldsMixin from '@/mixins/holds_mixin.js';
import { GRID_KEY, COMPANY_ID } from '@/config.js';
import PaymentSection from '@/components/call/PaymentSection.vue';
import PaymentMixin from '@/mixins/payment_mixin.js';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import DispatchUnitsController from '@/components/dispatchunits/Controller.vue';
import JobOffers from './JobOffers.vue';

import {
  get,
  find,
  isEmpty,
  debounce
} from 'lodash';

export default {
  name: 'dispatch',

  extends: RecordsView,

  mixins: [
    HoldsMixin,
    PaymentMixin
  ],

  components: {
    Grid,
    Actions,
    HoldsSection,
    PaymentSection,
    DispatchUnitsController,
    JobOffers
  },

  /* TODO: Handle these terminal settings
  TRMlDispatchRefresh_Default;
  TRMbAdvancedDriverTruckFilter;
  TRMbAllowCallNumAsInvoiceNum;

  UPRvc50LastLocationName
  UPRvc100DispatchDumpDir
  UPRlMaxFindRecords
  UPRlHighlightETA
  UPRlDispatchRefresh
  UPRLOClSubterminalKey
  */

  data () {
    return {
      viewConfig: {
        key: null,
        uuid: 'dispatch-screen',
        noun: 'Dispatches',
        recordKeyName: 'lCallKey',
        readRouteName: 'Call',
        requireFilters: false,
        dataAdditional: {
          SubcompanyKey: '', // Set in mounted()
          ShowApptCallMinutes: ''
        }
      },

      refreshInterval: 0,
      refreshTimer: null,
      actionableRecord: {},
      actionsVisible: false,
      dispatchUnitsVisible: false,
      showApptCallMinutesCache: '',
      allAppointmentsVisible: false,
      assignedCallGridKey: GRID_KEY.assigned,
      unassignedCallGridKey: GRID_KEY.unassigned,

      gridContainer: {
        endY: 0,
        startY: 0,
        maxHeight: 100,
        minHeight: 100,
        resizeTarget: {},
        isResizing: false
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'USER__state',
      'TOPSCOMPANY__settings'
    ]),

    unassignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.unassignedCallGridKey]);

      return settings || {};
    },

    unassignedGridData () {
      return get(this.viewData, `Grids[${this.unassignedCallGridKey}]`, []);
    },

    assignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.assignedCallGridKey]);

      return settings || {};
    },

    assignedGridData () {
      return get(this.viewData, `Grids[${this.assignedCallGridKey}]`, []);
    },

    appointmentsToggleClasses () {
      return {
        'fal': true,
        'fa-check-square': this.allAppointmentsVisible,
        'fa-square': !this.allAppointmentsVisible
      };
    },

    canAddCall () {
      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    }
  },

  methods: {
    resetTimer () {
      this.clearRefreshTimer();

      if (this.refreshInterval > 0) {
        this.refreshTimer = window.setInterval(() => {
          this.refresh();
        }, this.refreshInterval * 1000);
      }
    },

    clearRefreshTimer () {
      if (this.refreshTimer !== null) {
        window.clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },

    beforeLoadData () {
      return new Promise((resolve, reject) => {
        this.allAppointmentsVisible = window.sessionStorage.getItem('all-appointments-visible') === 'true';

        this.viewConfig.dataAdditional.ShowApptCallMinutes = this.allAppointmentsVisible
          ? ''
          : this.showApptCallMinutesCache;

        resolve();
      });
    },

    afterLoadData () {
      this.resetTimer();

      setTimeout(() => {
        this.initializeGridResizing();
      }, 1000);
    },

    afterGetTOPSCompanySettings () {
      this.setUserCompanySettings();
    },

    setUserCompanySettings () {
      let intervalProfile = get(this.USER__state.Profile, 'lDispatchRefresh', null);
      let intervalDefault = get(this.TOPSCOMPANY__settings, 'lDispatchRefresh_Default', null);
      let interval = intervalProfile || intervalDefault;

      this.refreshInterval = is.falsy(interval) ? 0 : interval;

      this.resetTimer();

      this.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.USER__state.Profile, 'lShowApptCall', '');

      if (isEmpty(this.viewConfig.dataAdditional.ShowApptCallMinutes)) {
        this.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.TOPSCOMPANY__settings, 'lShowApptCall_Default', '');
      }

      this.showApptCallMinutesCache = this.viewConfig.dataAdditional.ShowApptCallMinutes;
    },

    getActions (record) {
      this.actionableRecord = record;
      this.actionsVisible = true;
    },

    toggleActions () {
      if (is.truthy(this.actionsVisible)) this.refresh();

      this.actionsVisible = !this.actionsVisible;
    },

    addCall () {
      this.$router.push({ name: 'AddCall' });
    },

    notify (payload) {
      this.$router.replace({
        name: 'Notify',
        query: {
          callKey: this.$_.get(payload, 'callKey', ''),
          dispatchKey: this.$_.get(payload, 'dispatchKey', ''),
          dispatchDriverKey: this.$_.get(payload, 'dispatchDriverKey', ''),
          dispatchTruckKey: this.$_.get(payload, 'dispatchTruckKey', ''),
          dispatchEmployeeKey: this.$_.get(payload, 'dispatchEmployeeKey', ''),
          reason: this.$_.get(payload, 'reason', ''),
          returnTo: 'Dispatch'
        }
      });
    },

    toggleDispatchUnits () {
      this.dispatchUnitsVisible = !this.dispatchUnitsVisible;
    },

    handleKeyboardEvents (event) {
      if (event.altKey && event.keyCode === keyCodes.a) {  // Alt + A
        this.addCall();
      }
    },

    mouseDownHandler (event) {
      if (event.target.className === 'resizer') {
        this.gridContainer.isResizing = true;
        this.gridContainer.startY = event.y;
      }
    },

    mouseUpHandler (event) {
      this.gridContainer.isResizing = false;
    },

    mouseMoveHandler (event) {
      if (!this.gridContainer.isResizing) return;

      event.preventDefault();
      this.gridContainer.endY = event.y;
      this.gridContainer.resizeTarget.style['flex-basis'] = event.y + 'px';

      this.saveGridSize();
    },

    initializeGridResizing () {
      if (!isEmpty(this.gridContainer.resizeTarget)) return;

      this.$set(this.gridContainer, 'resizeTarget', document.querySelector('.unassigned'));

      if (is.truthy(window.sessionStorage.getItem('Dispatch-grid-size'))) {
        this.gridContainer.resizeTarget.style['flex-basis'] = window.sessionStorage.getItem('Dispatch-grid-size') + 'px';
      }
    },

    saveGridSize: debounce(function () {
      window.sessionStorage.setItem('Dispatch-grid-size', this.gridContainer.endY);
    }, 2 * 1000),

    toggleAllAppointments (value = !this.allAppointmentsVisible) {
      this.allAppointmentsVisible = value;
      this.viewConfig.dataAdditional.ShowApptCallMinutes = value ? '' : this.showApptCallMinutesCache;

      window.sessionStorage.setItem('all-appointments-visible', this.allAppointmentsVisible);

      this.loadData();
    }
  },

  created () {
    document.addEventListener('keydown', this.handleKeyboardEvents);
    document.addEventListener('mousedown', this.mouseDownHandler);
    document.addEventListener('mouseup', this.mouseUpHandler);
    document.addEventListener('mousemove', this.mouseMoveHandler);
  },

  mounted () {
    this.viewConfig.dataAdditional.SubcompanyKey = this.__state.user.Profile.lSubterminalKey || '';
    this.$store.state.addCall.shouldStayOnSave = false;
  },

  destroyed () {
    this.clearRefreshTimer();

    document.removeEventListener('keydown', this.handleKeyboardEvents);
    document.removeEventListener('mousedown', this.mouseDownHandler);
    document.removeEventListener('mouseup', this.mouseUpHandler);
    document.removeEventListener('mousemove', this.mouseMoveHandler);
  }
};
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: 1fr 20rlh;
  grid-template-rows: 1fr 5px 1fr;
  grid-template-areas:
    "unassigned joboffers"
    "resizer joboffers"
    "assigned joboffers";

  height: 100dvh;
  @include hide-scrollbars();

  .grid {
    overflow: hidden;

    @include hide-scrollbars();

    &.unassigned {
      grid-area: unassigned;
    }

    &.assigned {
      grid-area: assigned;
    }
  }

  .job-offers {
    grid-area: joboffers;

    border: 2px dotted orange;
    /* overflow: hidden; */
    /* @include hide-scrollbars(); */
  }

  .resizer {
    grid-area: resizer;

    background: var(--divider-bg);
    cursor: ns-resize;
  }
}
</style>
