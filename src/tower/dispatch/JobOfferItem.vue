<template>
  <div class="job-offer-item" :class="{
    expanded: isExpanded,
    'high-priority': job.priority >= 5,
    loading: actionLoading
  }">
    <!-- Job Summary -->
    <div class="job-summary" @click="toggleExpanded">
      <div class="job-header">
        <div class="job-title">
          <h5>{{ job.title }}</h5>
          <span v-if="job.priority >= 5" class="priority-indicator">
            <i class="fas fa-exclamation-triangle"></i>
            High Priority
          </span>
        </div>

        <div class="job-meta">
          <span v-if="job.expiration" class="expiration">
            <i class="fas fa-clock"></i>
            Expires: {{ formatExpiration(job.expiration) }}
          </span>
          <span class="status" :class="`status-${job.status}`">
            {{ job.status }}
          </span>
          <button class="expand-btn" :class="{ rotated: isExpanded }">
            <i class="fas fa-chevron-down"></i>
          </button>
        </div>
      </div>

      <div v-if="job.info" class="job-info">
        {{ job.info }}
      </div>
    </div>

    <!-- Job Details (Expanded) -->
    <div v-if="isExpanded" class="job-details">
      <!-- Loading State -->
      <div v-if="detailsLoading" class="details-loading">
        <i class="fas fa-spinner fa-spin"></i>
        Loading job details...
      </div>

      <!-- Job Details Content -->
      <div v-else-if="jobDetails" class="details-content">
        <div class="details-grid">
          <div v-if="jobDetails.description" class="detail-item">
            <label>Description:</label>
            <p>{{ jobDetails.description }}</p>
          </div>

          <div v-if="jobDetails.location" class="detail-item">
            <label>Location:</label>
            <p>{{ jobDetails.location }}</p>
          </div>

          <div v-if="jobDetails.customerInfo" class="detail-item">
            <label>Customer:</label>
            <p>{{ jobDetails.customerInfo }}</p>
          </div>

          <div v-if="jobDetails.vehicleInfo" class="detail-item">
            <label>Vehicle:</label>
            <p>{{ jobDetails.vehicleInfo }}</p>
          </div>

          <div v-if="jobDetails.assignedTo" class="detail-item">
            <label>Assigned To:</label>
            <p>{{ jobDetails.assignedTo }}</p>
          </div>

          <div v-if="jobDetails.dueDate" class="detail-item">
            <label>Due Date:</label>
            <p>{{ formatDate(jobDetails.dueDate) }}</p>
          </div>
        </div>

        <!-- Job Actions -->
        <div v-if="availableActions.length > 0" class="job-actions">
          <h6>Available Actions:</h6>
          <div class="actions-list">
            <button
              v-for="action in availableActions"
              :key="action.ResponseTag"
              @click="handleAction(action)"
              :disabled="actionLoading"
              class="action-btn"
              :class="`action-${action.ActionType || 'default'}`">
              <i v-if="actionLoading && selectedAction?.ResponseTag === action.ResponseTag"
                 class="fas fa-spinner fa-spin"></i>
              <i v-else :class="getActionIcon(action)"></i>
              {{ action.DisplayText }}
            </button>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="detailsError" class="details-error">
        <i class="fas fa-exclamation-triangle"></i>
        Failed to load job details
        <button @click="loadJobDetails" class="retry-btn">
          <i class="fas fa-refresh"></i>
          Retry
        </button>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmation" class="confirmation-overlay" @click.self="cancelConfirmation">
      <div class="confirmation-modal">
        <div class="confirmation-header">
          <h4>Confirm Action</h4>
          <button @click="cancelConfirmation" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="confirmation-body">
          <p>{{ confirmationMessage }}</p>
        </div>

        <div class="confirmation-footer">
          <button @click="cancelConfirmation" class="cancel-btn">
            Cancel
          </button>
          <button @click="confirmAction" :disabled="actionLoading" class="confirm-btn">
            <i v-if="actionLoading" class="fas fa-spinner fa-spin"></i>
            Confirm
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useJobOffersStore } from './jobOffers.store';

export default {
  name: 'JobOfferItem',

  props: {
    job: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      isExpanded: false,
      detailsLoading: false,
      detailsError: false,
      actionLoading: false,
      showConfirmation: false,
      confirmationMessage: '',
      selectedAction: null,
      availableActions: []
    };
  },

  computed: {
    store() {
      return useJobOffersStore();
    },

    jobDetails() {
      return this.store.selectedJob?.Key === this.job.Key ? this.store.selectedJob : null;
    }
  },

  methods: {
    async toggleExpanded() {
      this.isExpanded = !this.isExpanded;

      if (this.isExpanded && !this.jobDetails) {
        await this.loadJobDetails();
        await this.loadJobActions();
      }

      if (this.isExpanded) {
        this.$emit('job-selected', this.job);
      }
    },

    async loadJobDetails() {
      this.detailsLoading = true;
      this.detailsError = false;

      try {
        await this.store.selectJob(this.job.Key);
      } catch (error) {
        console.error('Failed to load job details:', error);
        this.detailsError = true;
      } finally {
        this.detailsLoading = false;
      }
    },

    async loadJobActions() {
      try {
        await this.store.fetchJobActions(this.job.Key);
        this.availableActions = this.store.getJobActions(this.job.Key);
      } catch (error) {
        console.error('Failed to load job actions:', error);
      }
    },

    handleAction(action) {
      this.selectedAction = action;

      if (action.ConfirmMessage) {
        this.confirmationMessage = action.ConfirmMessage;
        this.showConfirmation = true;
      } else {
        this.executeAction(action);
      }
    },

    async executeAction(action) {
      this.actionLoading = true;

      try {
        await this.store.handleJobAction(this.job.Key, action.ResponseTag, action.ResponseValue);

        this.$emit('job-action', {
          job: this.job,
          action: action
        });

        // Close expanded view after successful action
        this.isExpanded = false;
      } catch (error) {
        console.error('Failed to execute job action:', error);
        // Could show error message to user
      } finally {
        this.actionLoading = false;
        this.showConfirmation = false;
        this.selectedAction = null;
      }
    },

    confirmAction() {
      if (this.selectedAction) {
        this.executeAction(this.selectedAction);
      }
    },

    cancelConfirmation() {
      this.showConfirmation = false;
      this.selectedAction = null;
      this.confirmationMessage = '';
    },

    formatExpiration(expiration) {
      if (!expiration) return '';
      const date = new Date(expiration);
      return date.toLocaleString();
    },

    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    },

    getActionIcon(action) {
      const actionType = action.ActionType?.toLowerCase() || 'default';
      const iconMap = {
        accept: 'fas fa-check',
        reject: 'fas fa-times',
        assign: 'fas fa-user-plus',
        complete: 'fas fa-check-circle',
        cancel: 'fas fa-ban',
        default: 'fas fa-play'
      };
      return iconMap[actionType] || iconMap.default;
    }
  }
};
</script>

<style scoped>
.job-offer-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #fff;
  transition: all 0.2s;
  position: relative;
}

.job-offer-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.job-offer-item.expanded {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.job-offer-item.high-priority {
  border-left: 4px solid #dc2626;
}

.job-offer-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.job-summary {
  padding: 16px;
  cursor: pointer;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.job-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.job-title h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.priority-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #dc2626;
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.job-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.expiration {
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-completed {
  background: #e0e7ff;
  color: #3730a3;
}

.expand-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  transition: transform 0.2s;
}

.expand-btn.rotated {
  transform: rotate(180deg);
}

.job-info {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.job-details {
  border-top: 1px solid #e5e7eb;
  padding: 16px;
  background: #f9fafb;
}

.details-loading,
.details-error {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.details-error {
  color: #dc2626;
}

.retry-btn {
  margin-left: 8px;
  padding: 4px 8px;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.detail-item label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 14px;
}

.detail-item p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

.job-actions h6 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.actions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: #fff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-accept {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

.action-accept:hover {
  background: #059669;
  border-color: #059669;
}

.action-reject {
  background: #dc2626;
  border-color: #dc2626;
  color: #fff;
}

.action-reject:hover {
  background: #b91c1c;
  border-color: #b91c1c;
}

.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-modal {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
}

.confirmation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.confirmation-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.confirmation-body {
  padding: 20px;
}

.confirmation-body p {
  margin: 0;
  color: #374151;
  line-height: 1.5;
}

.confirmation-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-btn {
  background: #fff;
  border: 1px solid #d1d5db;
  color: #374151;
}

.cancel-btn:hover {
  background: #f3f4f6;
}

.confirm-btn {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: #fff;
}

.confirm-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
