import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useJobOffersStore } from './jobOffers.store';
import { debounce } from 'lodash';

/**
 * Composable for managing job offers and messages
 * Handles polling, data fetching, and user interactions
 */
export function useJobOffers(options = {}) {
  const {
    pollingInterval = 30000, // 30 seconds default
    autoStart = true,
    debounceDelay = 300
  } = options;

  const store = useJobOffersStore();
  const pollingTimer = ref(null);
  const isPolling = ref(false);

  // Reactive state from store
  const jobs = computed(() => store.jobs);
  const messages = computed(() => store.messages);
  const selectedJob = computed(() => store.selectedJob);
  const loading = computed(() => store.loading);
  const error = computed(() => store.error);
  const hasData = computed(() => store.hasData);
  const urgentMessages = computed(() => store.urgentMessages);
  const highPriorityJobs = computed(() => store.highPriorityJobs);

  // Debounced methods to prevent rapid API calls
  const debouncedLoadJobs = debounce(async () => {
    await store.fetchNewOffers();
  }, debounceDelay);

  const debouncedGetJobDetails = debounce(async (jobKey) => {
    await store.selectJob(jobKey);
  }, debounceDelay);

  const debouncedGetJobActions = debounce(async (jobKey) => {
    await store.fetchJobActions(jobKey);
  }, debounceDelay);

  /**
   * Load jobs and messages from API
   */
  async function loadJobs() {
    return debouncedLoadJobs();
  }

  /**
   * Get detailed information for a specific job
   */
  async function getJobDetails(jobKey) {
    if (!jobKey) return null;
    return debouncedGetJobDetails(jobKey);
  }

  /**
   * Get possible actions for a specific job
   */
  async function getJobActions(jobKey) {
    if (!jobKey) return [];
    await debouncedGetJobActions(jobKey);
    return store.getJobActions(jobKey);
  }

  /**
   * Handle a job action (Accept, Reject, etc.)
   */
  async function handleJobAction(jobKey, responseTag, responseValue) {
    if (!jobKey || !responseTag) {
      throw new Error('Job key and response tag are required');
    }

    return store.handleJobAction(jobKey, responseTag, responseValue);
  }

  /**
   * Acknowledge a message
   */
  async function acknowledgeMessage(messageKey) {
    if (!messageKey) {
      throw new Error('Message key is required');
    }

    return store.acknowledgeMessage(messageKey);
  }

  /**
   * Start polling for new job offers
   */
  function startPolling() {
    if (isPolling.value) return;

    isPolling.value = true;
    
    // Initial load
    loadJobs();

    // Set up polling timer
    pollingTimer.value = setInterval(() => {
      if (!store.loading) {
        loadJobs();
      }
    }, pollingInterval);
  }

  /**
   * Stop polling for job offers
   */
  function stopPolling() {
    if (pollingTimer.value) {
      clearInterval(pollingTimer.value);
      pollingTimer.value = null;
    }
    isPolling.value = false;
  }

  /**
   * Restart polling (useful for error recovery)
   */
  function restartPolling() {
    stopPolling();
    startPolling();
  }

  /**
   * Clear any errors
   */
  function clearError() {
    store.clearError();
  }

  /**
   * Reset all data
   */
  function reset() {
    stopPolling();
    store.reset();
  }

  /**
   * Get actions for a specific job (cached)
   */
  function getCachedJobActions(jobKey) {
    return store.getJobActions(jobKey);
  }

  /**
   * Check if job has actions loaded
   */
  function hasJobActions(jobKey) {
    return getCachedJobActions(jobKey).length > 0;
  }

  // Lifecycle management
  onMounted(() => {
    if (autoStart) {
      startPolling();
    }
  });

  onUnmounted(() => {
    stopPolling();
  });

  return {
    // State
    jobs,
    messages,
    selectedJob,
    loading,
    error,
    hasData,
    urgentMessages,
    highPriorityJobs,
    isPolling,

    // Methods
    loadJobs,
    getJobDetails,
    getJobActions,
    handleJobAction,
    acknowledgeMessage,
    startPolling,
    stopPolling,
    restartPolling,
    clearError,
    reset,
    getCachedJobActions,
    hasJobActions
  };
}
