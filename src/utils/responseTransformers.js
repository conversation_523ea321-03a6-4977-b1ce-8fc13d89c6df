import { isNumber, isBoolean, forEach } from 'lodash';

/**
 * Pre-built transformation schemas for common TOPS API responses
 */
export const transformSchemas = {
  // Common key transformation
  keyFields: {
    Key: 'number',
    TypeKey: 'number',
    StatusKey: 'number',
    CustomerKey: 'number',
    EmployeeKey: 'number',
    Truck<PERSON>ey: 'number',
    <PERSON><PERSON>ey: 'number',
    LotKey: 'number',
    ServiceKey: 'number'
  },

  // Boolean fields that come as strings
  booleanFields: {
    Active: 'boolean',
    Required: 'boolean',
    IsVisible: 'boolean',
    DataAvailable: 'boolean',
    EmailAvailable: 'boolean',
    EmailToCustomersAvailable: 'boolean',
    ShowCityStateForGeocoding: 'boolean',
    Retow: 'boolean',
    ReleasePaymentRequired: 'boolean',
    AskToReleaseHolds: 'boolean',
    AskToTerminateLien: 'boolean',
    AskToRemoveLienPricing: 'boolean'
  },

  // Numeric fields that come as strings
  numericFields: {
    TaxRate: 'number',
    TaxRateOverride: 'number',
    DiscountPct: 'number',
    Total: 'number',
    TaxTotal: 'number',
    DiscountTotal: 'number',
    TowBalance: 'number',
    SaleTaxRate: 'number',
    Order: 'number',
    ValueTypeKey: 'number'
  },

  // Geographic coordinates
  coordinates: {
    Lat: 'number',
    Lon: 'number',
    SubcompanyLat: 'number',
    SubcompanyLon: 'number'
  },

  // Label/printing settings
  labelSettings: {
    bPortrait: 'boolean',
    tNumLabelsAcross: 'number',
    tNumLabelsDown: 'number',
    fLabelWidth: 'number',
    fLabelHeight: 'number',
    fLeftMargin: 'number',
    fTopMargin: 'number',
    fHztlSpacingBetweenLabels: 'number'
  }
};

/**
 * Core transformation functions for schema-based transforms
 */
export const transformers = {
  // Convert string to number, handling empty strings and null
  toNumber: (value) => {
    if (value === '' || value === null || value === undefined) return null;
    const num = Number(value);
    return isNaN(num) ? value : num;
  },

  // Convert string to boolean (handles '1', 'true', etc.)
  toBoolean: (value) => {
    if (typeof value === 'boolean') return value;
    return value === 'true' || value === '1' || value === 1;
  },

  // Convert string to JSON, fallback to original value
  toJson: (value) => {
    if (typeof value !== 'string' || value === '') return value;
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  },

  // Convert to integer specifically
  toInt: (value) => {
    if (value === '' || value === null || value === undefined) return null;
    const num = parseInt(value, 10);
    return isNaN(num) ? value : num;
  },

  // Convert to float specifically
  toFloat: (value) => {
    if (value === '' || value === null || value === undefined) return null;
    const num = parseFloat(value);
    return isNaN(num) ? value : num;
  },

  // Transform arrays of objects using a schema
  transformArray: (itemSchema) => (array) => {
    if (!Array.isArray(array)) return array;
    return array.map(item => {
      if (typeof itemSchema === 'object' && itemSchema !== null) {
        const transformed = { ...item };
        Object.keys(itemSchema).forEach(key => {
          if (key in transformed) {
            const transform = itemSchema[key];
            if (typeof transform === 'function') {
              transformed[key] = transform(transformed[key]);
            } else if (typeof transform === 'string') {
              transformed[key] = applyBuiltInTransform(transformed[key], transform);
            }
          }
        });
        return transformed;
      }
      return item;
    });
  },

  // Conditional transformer - only transform if condition is met
  when: (condition, transformer) => (value) => {
    if (typeof condition === 'function' ? condition(value) : condition) {
      return typeof transformer === 'function' ? transformer(value) : value;
    }
    return value;
  }
};

// Helper function for built-in transform types
function applyBuiltInTransform(value, type) {
  switch (type) {
    case 'number':
      return transformers.toNumber(value);
    case 'boolean':
      return transformers.toBoolean(value);
    case 'json':
      return transformers.toJson(value);
    case 'string':
      return String(value);
    case 'float':
      return transformers.toFloat(value);
    case 'int':
      return transformers.toInt(value);
    default:
      return value;
  }
}

/**
 * Common transformation patterns
 */
export const commonResponseTransforms = {
  // Transform employee/driver/truck lists
  employeeList: {
    Data: transformers.transformArray({
      Key: transformers.toNumber,
      Active: transformers.toBoolean
    })
  },

  // Transform status lists
  statusList: transformers.transformArray({
    Key: transformers.toNumber
  }),

  // Transform report lists
  reportList: transformers.transformArray({
    Key: transformers.toNumber,
    TypeKey: transformers.toNumber,
    DataAvailable: transformers.toBoolean,
    EmailAvailable: transformers.toBoolean,
    EmailToCustomersAvailable: transformers.toBoolean
  }),

  // Transform inspection items
  inspectionItems: transformers.transformArray({
    Key: transformers.when(val => !isNumber(val), transformers.toNumber),
    ValueTypeKey: transformers.when(val => !isNumber(val), transformers.toNumber),
    Order: transformers.when(val => !isNumber(val), transformers.toNumber),
    Required: transformers.when(val => !isBoolean(val), transformers.toBoolean),
    Active: transformers.when(val => !isBoolean(val), transformers.toBoolean),
    PossibleValues: transformers.transformArray({
      Active: transformers.when(val => !isBoolean(val), transformers.toBoolean)
    })
  }),

  // Transform lien steps
  lienSteps: transformers.transformArray({
    bActive: (val) => Number(val) === 1,
    bRequired: (val) => Number(val) === 1,
    iActivationQty: transformers.toNumber,
    lActivationBasisTypeKey: transformers.toNumber,
    lActivationUnitsTypeKey: transformers.toNumber,
    lLienProcessKey: transformers.toNumber,
    lLienStepKey: transformers.toNumber,
    tOrder: transformers.toNumber
  }),

  // Transform location data with coordinates
  locationData: {
    ...transformSchemas.coordinates,
    Location: transformSchemas.coordinates,
    Destination: transformSchemas.coordinates
  },

  // Transform dispatch details with thumbnail arrays
  dispatchDetails: {
    Data: transformers.transformArray({
      Key: 'number'
    })
  },

  // Transform system tags
  systemTags: {
    Data: transformers.transformArray({
      Modifiable: 'boolean'
    })
  },

  // Transform call data with common fields
  callData: {
    ...transformSchemas.keyFields,
    ...transformSchemas.booleanFields,
    ...transformSchemas.numericFields,
    ...transformSchemas.coordinates
  },

  // Transform customer data
  customerData: {
    ...transformSchemas.keyFields,
    ...transformSchemas.booleanFields,
    ...transformSchemas.numericFields
  },

  // Transform pricing data
  pricingData: {
    ...transformSchemas.numericFields,
    ...transformSchemas.booleanFields
  }
};

/**
 * Helper function to create a combined schema from multiple schemas
 */
export function combineSchemas (...schemas) {
  return Object.assign({}, ...schemas);
}

/**
 * Helper function to create a schema for array transformation
 */
export function arraySchema (itemSchema) {
  return transformers.transformArray(itemSchema);
}

/**
 * Helper function to create conditional transformations
 */
export function conditionalTransform (condition, trueTransform, falseTransform = null) {
  return (value) => {
    const shouldTransform = typeof condition === 'function' ? condition(value) : condition;
    if (shouldTransform) {
      return typeof trueTransform === 'function' ? trueTransform(value) : trueTransform;
    }
    return falseTransform ? (typeof falseTransform === 'function' ? falseTransform(value) : falseTransform) : value;
  };
}

/**
 * Apply transformation schema to response data (for use with new TOPS API client)
 * This is a standalone function for transforming data after receiving a response
 */
export function applyResponseTransformSchema (data, schema) {
  if (!schema || typeof schema !== 'object') return data;

  const transformedData = { ...data };

  Object.keys(schema).forEach(key => {
    if (key in transformedData) {
      const transformer = schema[key];
      if (typeof transformer === 'function') {
        transformedData[key] = transformer(transformedData[key]);
      } else if (typeof transformer === 'string') {
        transformedData[key] = applyBuiltInTransform(transformedData[key], transformer);
      } else if (typeof transformer === 'object' && transformer !== null) {
        // Nested schema for objects/arrays
        if (Array.isArray(transformedData[key])) {
          transformedData[key] = transformedData[key].map(item => applyResponseTransformSchema(item, transformer));
        } else if (typeof transformedData[key] === 'object' && transformedData[key] !== null) {
          transformedData[key] = applyResponseTransformSchema(transformedData[key], transformer);
        }
      }
    }
  });

  return transformedData;
}
